package com.javaweb.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.SummaryHouseStats;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.HouseInfoMapper;
import com.javaweb.system.mapper.SummaryHouseStatsMapper;
import com.javaweb.system.query.HouseStatsAnalysisQuery;
import com.javaweb.system.service.IHouseStatsAnalysisService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 住户统计分析服务实现类
 * 提供住户阀门状态和温度分布的统计分析功能
 * </p>
 *
 * @Date: 2025/01/01
 */
@Service
public class HouseStatsAnalysisServiceImpl implements IHouseStatsAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(HouseStatsAnalysisServiceImpl.class);

    @Autowired
    private SummaryHouseStatsMapper summaryHouseStatsMapper;

    @Autowired
    private HouseInfoMapper houseInfoMapper;

    @Autowired
    private HeatUnitMapper heatUnitMapper;

    // 标准温度区间定义
    private static final String[] STANDARD_TEMP_RANGES = {
        "低于16度", "16-18度", "18-20度", "20-22度", "高于22度"
    };

    // 阀门挡位标准定义
    private static final String[] VALVE_GEAR_RANGES = {
        "0档", "1档", "2档", "3档", "4档", "5档"
    };

    // 阀门开度标准定义
    private static final String[] VALVE_OPENING_RANGES = {
        "0%", "1-25%", "26-50%", "51-75%", "76-100%"
    };

    @Override
    public Map<String, Object> getAnalysisData(HouseStatsAnalysisQuery query) {
        logger.info("【住户统计分析】开始获取分析数据，小区ID: {}, 时间范围: {} - {}", 
                   query.getHeatUnitId(), query.getStartDate(), query.getEndDate());

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取概览统计数据
            Map<String, Object> summaryData = getSummaryData(query.getHeatUnitId());
            result.put("summary", summaryData);

            // 2. 获取详细统计数据列表
            Map<String, Object> listData = getStatsList(query);
            result.put("list", listData.get("list"));
            result.put("total", listData.get("total"));

            // 3. 获取图表数据
            Map<String, Object> valveChartData = getValveChartData(query);
            result.put("valveChartData", valveChartData);

            Map<String, Object> tempChartData = getTempChartData(query);
            result.put("tempChartData", tempChartData);

            Map<String, Object> trendChartData = getTrendChartData(query);
            result.put("trendChartData", trendChartData);

            logger.info("【住户统计分析】数据获取成功，返回记录数: {}", listData.get("total"));

        } catch (Exception e) {
            logger.error("【住户统计分析】获取分析数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取分析数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getSummaryData(String heatUnitId) {
        logger.info("【住户统计分析】获取小区概览数据，小区ID: {}", heatUnitId);

        Map<String, Object> summaryData = new HashMap<>();

        try {
            // 获取小区信息
            HeatUnit heatUnit = heatUnitMapper.selectById(heatUnitId);
            if (heatUnit != null) {
                summaryData.put("heatUnitName", heatUnit.getName());
            }

            // 统计总住户数
            QueryWrapper<HouseInfo> houseQuery = new QueryWrapper<>();
            houseQuery.eq("heat_unit_id", heatUnitId);
            int totalHouses = houseInfoMapper.selectCount(houseQuery);
            summaryData.put("totalHouses", totalHouses);

            // 统计在线阀门数（有阀门编号的住户）
            QueryWrapper<HouseInfo> valveQuery = new QueryWrapper<>();
            valveQuery.eq("heat_unit_id", heatUnitId)
                     .isNotNull("valves_no")
                     .ne("valves_no", "");
            int onlineValves = houseInfoMapper.selectCount(valveQuery);
            summaryData.put("onlineValves", onlineValves);

            // 统计温度传感器数（有温度采集器编号的住户）
            QueryWrapper<HouseInfo> tempQuery = new QueryWrapper<>();
            tempQuery.eq("heat_unit_id", heatUnitId)
                    .isNotNull("indoor_t_no")
                    .ne("indoor_t_no", "")
                     .ne("indoor_t_no", "null");
            int tempSensors = houseInfoMapper.selectCount(tempQuery);
            summaryData.put("tempSensors", tempSensors);

            // 获取最新的平均温度（从最新的统计数据中计算）
            QueryWrapper<SummaryHouseStats> statsQuery = new QueryWrapper<>();
            statsQuery.eq("heat_unit_id", heatUnitId)
                     .orderByDesc("stat_time")
                     .last("LIMIT 1");
            SummaryHouseStats latestStats = summaryHouseStatsMapper.selectOne(statsQuery);
            
            double avgTemp = 0.0;
            if (latestStats != null && latestStats.getTempStats() != null) {
                avgTemp = calculateAverageTemp(latestStats.getTempStats());
            }
            summaryData.put("avgTemp", Math.round(avgTemp * 10.0) / 10.0);

            // 计算设备在线率
            double onlineRate = totalHouses > 0 ? (double) onlineValves / totalHouses * 100 : 0;
            summaryData.put("onlineRate", Math.round(onlineRate * 10.0) / 10.0);

            logger.info("【住户统计分析】概览数据获取成功，总住户: {}, 在线阀门: {}, 温度传感器: {}", 
                       totalHouses, onlineValves, tempSensors);

        } catch (Exception e) {
            logger.error("【住户统计分析】获取概览数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取概览数据失败: " + e.getMessage());
        }

        return summaryData;
    }

    @Override
    public Map<String, Object> getValveChartData(HouseStatsAnalysisQuery query) {
        logger.info("【住户统计分析】获取阀门状态分布数据，小区ID: {}", query.getHeatUnitId());

        Map<String, Object> result = new HashMap<>();

        try {
            // 确定实际使用的数据类型
            String actualDataType = determineValveDataType(query);
            result.put("dataType", actualDataType);

            // 获取最新的统计数据
            QueryWrapper<SummaryHouseStats> queryWrapper = buildStatsQuery(query);
            queryWrapper.orderByDesc("stat_time").last("LIMIT 1");
            
            SummaryHouseStats latestStats = summaryHouseStatsMapper.selectOne(queryWrapper);
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            Map<String, Object> summary = new HashMap<>();

            if (latestStats != null && latestStats.getValveStats() != null) {
                JSONObject valveStats = JSON.parseObject(latestStats.getValveStats());
                
                // 根据数据类型提取相应的统计数据
                JSONObject targetStats = null;
                if ("gear".equals(actualDataType) && valveStats.containsKey("gear")) {
                    targetStats = valveStats.getJSONObject("gear");
                } else if ("opening".equals(actualDataType) && valveStats.containsKey("percent")) {
                    targetStats = valveStats.getJSONObject("percent");
                }

                if (targetStats != null) {
                    int totalCount = 0;
                    for (String key : targetStats.keySet()) {
                        int value = targetStats.getIntValue(key);
                        totalCount += value;
                        
                        Map<String, Object> item = new HashMap<>();
                        item.put("name", key);
                        item.put("value", value);
                        chartData.add(item);
                    }
                    
                    summary.put("totalCount", totalCount);
                    summary.put("categories", targetStats.size());
                }
            }

            result.put("chartData", chartData);
            result.put("summary", summary);

            logger.info("【住户统计分析】阀门状态分布数据获取成功，数据类型: {}, 分类数: {}", 
                       actualDataType, chartData.size());

        } catch (Exception e) {
            logger.error("【住户统计分析】获取阀门状态分布数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取阀门状态分布数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getTempChartData(HouseStatsAnalysisQuery query) {
        logger.info("【住户统计分析】获取温度区间分布数据，小区ID: {}", query.getHeatUnitId());

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取最新的统计数据
            QueryWrapper<SummaryHouseStats> queryWrapper = buildStatsQuery(query);
            queryWrapper.orderByDesc("stat_time").last("LIMIT 1");
            
            SummaryHouseStats latestStats = summaryHouseStatsMapper.selectOne(queryWrapper);
            
            List<String> categories = new ArrayList<>();
            List<Integer> values = new ArrayList<>();
            Map<String, Object> summary = new HashMap<>();

            if (latestStats != null && latestStats.getTempStats() != null) {
                JSONObject tempStats = JSON.parseObject(latestStats.getTempStats());
                
                // 按标准温度区间排序
                List<String> orderedRanges = Arrays.asList(STANDARD_TEMP_RANGES);
                int totalCount = 0;
                
                for (String range : orderedRanges) {
                    if (tempStats.containsKey(range)) {
                        int value = tempStats.getIntValue(range);
                        categories.add(range);
                        values.add(value);
                        totalCount += value;
                    }
                }
                
                summary.put("totalCount", totalCount);
                summary.put("categories", categories.size());
            }

            result.put("categories", categories);
            result.put("values", values);
            result.put("summary", summary);

            logger.info("【住户统计分析】温度区间分布数据获取成功，区间数: {}", categories.size());

        } catch (Exception e) {
            logger.error("【住户统计分析】获取温度区间分布数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取温度区间分布数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getTrendChartData(HouseStatsAnalysisQuery query) {
        logger.info("【住户统计分析】获取趋势分析数据，小区ID: {}", query.getHeatUnitId());

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取时间范围内的统计数据
            QueryWrapper<SummaryHouseStats> queryWrapper = buildStatsQuery(query);
            queryWrapper.orderByAsc("stat_time");

            List<SummaryHouseStats> statsList = summaryHouseStatsMapper.selectList(queryWrapper);

            List<String> categories = new ArrayList<>();
            List<String> legend = new ArrayList<>();
            List<Map<String, Object>> series = new ArrayList<>();

            if (!statsList.isEmpty()) {
                // 确定数据类型和图例
                String actualDataType = determineValveDataType(query);
                Set<String> allKeys = new LinkedHashSet<>();

                // 收集所有可能的状态键
                for (SummaryHouseStats stats : statsList) {
                    if (stats.getValveStats() != null) {
                        JSONObject valveStats = JSON.parseObject(stats.getValveStats());
                        JSONObject targetStats = null;

                        if ("gear".equals(actualDataType) && valveStats.containsKey("gear")) {
                            targetStats = valveStats.getJSONObject("gear");
                        } else if ("opening".equals(actualDataType) && valveStats.containsKey("percent")) {
                            targetStats = valveStats.getJSONObject("percent");
                        }

                        if (targetStats != null) {
                            allKeys.addAll(targetStats.keySet());
                        }
                    }
                }

                legend.addAll(allKeys);

                // 构建时间轴
                SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm");
                for (SummaryHouseStats stats : statsList) {
                    categories.add(sdf.format(stats.getStatTime()));
                }

                // 构建系列数据
                for (String key : allKeys) {
                    Map<String, Object> seriesItem = new HashMap<>();
                    seriesItem.put("name", key);
                    seriesItem.put("type", "line");
                    seriesItem.put("smooth", true);

                    List<Integer> data = new ArrayList<>();
                    for (SummaryHouseStats stats : statsList) {
                        int value = 0;
                        if (stats.getValveStats() != null) {
                            JSONObject valveStats = JSON.parseObject(stats.getValveStats());
                            JSONObject targetStats = null;

                            if ("gear".equals(actualDataType) && valveStats.containsKey("gear")) {
                                targetStats = valveStats.getJSONObject("gear");
                            } else if ("opening".equals(actualDataType) && valveStats.containsKey("percent")) {
                                targetStats = valveStats.getJSONObject("percent");
                            }

                            if (targetStats != null && targetStats.containsKey(key)) {
                                value = targetStats.getIntValue(key);
                            }
                        }
                        data.add(value);
                    }

                    seriesItem.put("data", data);
                    series.add(seriesItem);
                }
            }

            result.put("categories", categories);
            result.put("legend", legend);
            result.put("series", series);

            logger.info("【住户统计分析】趋势分析数据获取成功，时间点数: {}, 系列数: {}",
                       categories.size(), series.size());

        } catch (Exception e) {
            logger.error("【住户统计分析】获取趋势分析数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取趋势分析数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public void exportData(HouseStatsAnalysisQuery query, HttpServletResponse response) {
        logger.info("【住户统计分析】开始导出数据，小区ID: {}", query.getHeatUnitId());

        try {
            // 获取小区信息
            HeatUnit heatUnit = heatUnitMapper.selectById(query.getHeatUnitId());
            String heatUnitName = heatUnit != null ? heatUnit.getName() : "未知小区";

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 1. 创建概览统计表
            createSummarySheet(workbook, headerStyle, dataStyle, query, heatUnitName);

            // 2. 创建阀门状态分布表
            createValveStatsSheet(workbook, headerStyle, dataStyle, query);

            // 3. 创建温度区间分布表
            createTempStatsSheet(workbook, headerStyle, dataStyle, query);

            // 4. 如果需要详细数据，创建详细统计表
            if (query.getExportDetail()) {
                createDetailStatsSheet(workbook, headerStyle, dataStyle, query);
            }

            // 设置响应头
            String fileName = String.format("住户统计分析_%s_%s.xlsx",
                                           heatUnitName,
                                           LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition",
                             "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();

            logger.info("【住户统计分析】数据导出成功，文件名: {}", fileName);

        } catch (Exception e) {
            logger.error("【住户统计分析】导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getLatestData(String heatUnitId) {
        logger.info("【住户统计分析】获取最新数据，小区ID: {}", heatUnitId);

        Map<String, Object> result = new HashMap<>();

        try {
            QueryWrapper<SummaryHouseStats> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("heat_unit_id", heatUnitId)
                       .orderByDesc("stat_time")
                       .last("LIMIT 1");

            SummaryHouseStats latestStats = summaryHouseStatsMapper.selectOne(queryWrapper);

            if (latestStats != null) {
                result.put("latestStatTime", latestStats.getStatTime());
                result.put("valveStats", latestStats.getValveStats());
                result.put("tempStats", latestStats.getTempStats());
                result.put("updateTime", latestStats.getUpdatedAt());
            }

            logger.info("【住户统计分析】最新数据获取成功");

        } catch (Exception e) {
            logger.error("【住户统计分析】获取最新数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取最新数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getCompareData(HouseStatsAnalysisQuery query) {
        logger.info("【住户统计分析】获取对比数据");

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有小区列表
            List<String> heatUnitIds = summaryHouseStatsMapper.selectAllHeatUnitIds();
            List<Map<String, Object>> heatUnits = new ArrayList<>();
            List<Map<String, Object>> compareData = new ArrayList<>();

            for (String heatUnitId : heatUnitIds) {
                HeatUnit heatUnit = heatUnitMapper.selectById(heatUnitId);
                if (heatUnit != null) {
                    Map<String, Object> unitInfo = new HashMap<>();
                    unitInfo.put("id", heatUnitId);
                    unitInfo.put("name", heatUnit.getName());
                    heatUnits.add(unitInfo);

                    // 获取该小区的概览数据
                    Map<String, Object> summaryData = getSummaryData(heatUnitId);
                    summaryData.put("heatUnitId", heatUnitId);
                    summaryData.put("heatUnitName", heatUnit.getName());
                    compareData.add(summaryData);
                }
            }

            result.put("heatUnits", heatUnits);
            result.put("compareData", compareData);

            logger.info("【住户统计分析】对比数据获取成功，小区数: {}", heatUnits.size());

        } catch (Exception e) {
            logger.error("【住户统计分析】获取对比数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取对比数据失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public boolean hasStatsData(String heatUnitId, String startDate, String endDate) {
        QueryWrapper<SummaryHouseStats> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("heat_unit_id", heatUnitId);

        if (startDate != null && !startDate.trim().isEmpty()) {
            queryWrapper.ge("stat_time", startDate + " 00:00:00");
        }
        if (endDate != null && !endDate.trim().isEmpty()) {
            queryWrapper.le("stat_time", endDate + " 23:59:59");
        }

        return summaryHouseStatsMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public String getActualValveDataType(String heatUnitId) {
        QueryWrapper<SummaryHouseStats> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("heat_unit_id", heatUnitId)
                   .orderByDesc("stat_time")
                   .last("LIMIT 1");

        SummaryHouseStats latestStats = summaryHouseStatsMapper.selectOne(queryWrapper);

        if (latestStats != null && latestStats.getValveStats() != null) {
            JSONObject valveStats = JSON.parseObject(latestStats.getValveStats());

            // 检查开度数据是否有效（不全为0）
            if (valveStats.containsKey("percent")) {
                JSONObject percentStats = valveStats.getJSONObject("percent");
                if (percentStats != null && !percentStats.isEmpty()) {
                    // 检查是否有非零的开度数据
                    boolean hasValidPercentData = false;
                    for (String key : percentStats.keySet()) {
                        int value = percentStats.getIntValue(key);
                        if (value > 0 && !"0%".equals(key)) {
                            hasValidPercentData = true;
                            break;
                        }
                    }
                    if (hasValidPercentData) {
                        return "opening";
                    }
                }
            }

            // 检查挡位数据是否有效
            if (valveStats.containsKey("gear")) {
                JSONObject gearStats = valveStats.getJSONObject("gear");
                if (gearStats != null && !gearStats.isEmpty()) {
                    // 检查是否有挡位数据
                    boolean hasValidGearData = false;
                    for (String key : gearStats.keySet()) {
                        int value = gearStats.getIntValue(key);
                        if (value > 0) {
                            hasValidGearData = true;
                            break;
                        }
                    }
                    if (hasValidGearData) {
                        return "gear";
                    }
                }
            }
        }

        return "none";
    }

    @Override
    public void refreshStatsCache(String heatUnitId) {
        // 这里可以实现缓存刷新逻辑
        logger.info("【住户统计分析】刷新统计数据缓存，小区ID: {}", heatUnitId);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取统计数据列表
     */
    private Map<String, Object> getStatsList(HouseStatsAnalysisQuery query) {
        Map<String, Object> result = new HashMap<>();

        // 构建查询条件
        QueryWrapper<SummaryHouseStats> queryWrapper = buildStatsQuery(query);
        queryWrapper.orderByDesc("stat_time");

        // 分页查询
        Page<SummaryHouseStats> page = new Page<>(query.getPage(), query.getLimit());
        IPage<SummaryHouseStats> pageResult = summaryHouseStatsMapper.selectPage(page, queryWrapper);

        // 处理结果数据
        List<Map<String, Object>> list = new ArrayList<>();
        for (SummaryHouseStats stats : pageResult.getRecords()) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", stats.getId());
            item.put("heatUnitId", stats.getHeatUnitId());
            item.put("statTime", stats.getStatTime());
            item.put("valveStats", stats.getValveStats());
            item.put("tempStats", stats.getTempStats());

            // 获取小区名称
            HeatUnit heatUnit = heatUnitMapper.selectById(stats.getHeatUnitId());
            item.put("heatUnitName", heatUnit != null ? heatUnit.getName() : "未知小区");

            list.add(item);
        }

        result.put("list", list);
        result.put("total", pageResult.getTotal());

        return result;
    }

    /**
     * 构建统计查询条件
     */
    private QueryWrapper<SummaryHouseStats> buildStatsQuery(HouseStatsAnalysisQuery query) {
        QueryWrapper<SummaryHouseStats> queryWrapper = new QueryWrapper<>();

        // 小区ID条件
        if (query.getHeatUnitId() != null && !query.getHeatUnitId().trim().isEmpty()) {
            queryWrapper.eq("heat_unit_id", query.getHeatUnitId());
        }

        // 时间范围条件
        if (query.getStartDate() != null && !query.getStartDate().trim().isEmpty()) {
            queryWrapper.ge("stat_time", query.getStartDate() + " 00:00:00");
        }
        if (query.getEndDate() != null && !query.getEndDate().trim().isEmpty()) {
            queryWrapper.le("stat_time", query.getEndDate() + " 23:59:59");
        }

        return queryWrapper;
    }

    /**
     * 确定阀门数据类型
     */
    private String determineValveDataType(HouseStatsAnalysisQuery query) {
        String dataType = query.getValidValveDataType();

        if ("auto".equals(dataType)) {
            // 自动判断数据类型
            return getActualValveDataType(query.getHeatUnitId());
        }

        return dataType;
    }

    /**
     * 计算平均温度
     */
    private double calculateAverageTemp(String tempStatsJson) {
        try {
            JSONObject tempStats = JSON.parseObject(tempStatsJson);
            double totalTemp = 0.0;
            int totalCount = 0;

            for (String range : STANDARD_TEMP_RANGES) {
                if (tempStats.containsKey(range)) {
                    int count = tempStats.getIntValue(range);
                    double avgRangeTemp = getAverageRangeTemp(range);
                    totalTemp += avgRangeTemp * count;
                    totalCount += count;
                }
            }

            return totalCount > 0 ? totalTemp / totalCount : 0.0;
        } catch (Exception e) {
            logger.error("计算平均温度失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取温度区间的平均值
     */
    private double getAverageRangeTemp(String range) {
        switch (range) {
            case "低于16度": return 15.0;
            case "16-18度": return 17.0;
            case "18-20度": return 19.0;
            case "20-22度": return 21.0;
            case "高于22度": return 23.0;
            default: return 20.0;
        }
    }

    /**
     * 创建Excel表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        return style;
    }

    /**
     * 创建Excel数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 创建概览统计表
     */
    private void createSummarySheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle,
                                   HouseStatsAnalysisQuery query, String heatUnitName) {
        Sheet sheet = workbook.createSheet("概览统计");

        // 获取概览数据
        Map<String, Object> summaryData = getSummaryData(query.getHeatUnitId());

        int rowNum = 0;

        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("住户统计分析概览 - " + heatUnitName);
        titleCell.setCellStyle(headerStyle);

        // 空行
        rowNum++;

        // 数据行
        String[][] data = {
            {"总住户数", String.valueOf(summaryData.get("totalHouses"))},
            {"在线阀门数", String.valueOf(summaryData.get("onlineValves"))},
            {"温度传感器数", String.valueOf(summaryData.get("tempSensors"))},
            {"平均温度", summaryData.get("avgTemp") + "°C"},
            {"设备在线率", summaryData.get("onlineRate") + "%"}
        };

        for (String[] rowData : data) {
            Row row = sheet.createRow(rowNum++);
            for (int i = 0; i < rowData.length; i++) {
                Cell cell = row.createCell(i);
                cell.setCellValue(rowData[i]);
                cell.setCellStyle(i == 0 ? headerStyle : dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < 2; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建阀门状态分布表
     */
    private void createValveStatsSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle,
                                      HouseStatsAnalysisQuery query) {
        Map<String, Object> valveData = getValveChartData(query);
        String dataType = (String) valveData.get("dataType");

        // 只有在有阀门数据时才创建表格
        if (!"none".equals(dataType)) {
            Sheet sheet = workbook.createSheet("阀门状态分布");

            int rowNum = 0;

            // 标题行
            Row titleRow = sheet.createRow(rowNum++);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("阀门状态分布统计 (" + ("gear".equals(dataType) ? "挡位" : "开度") + ")");
            titleCell.setCellStyle(headerStyle);

            // 空行
            rowNum++;

            // 表头
            Row headerRow = sheet.createRow(rowNum++);
            String[] headers = {"状态", "住户数量", "占比"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 数据行
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> chartData = (List<Map<String, Object>>) valveData.get("chartData");
            int totalCount = chartData.stream().mapToInt(item -> (Integer) item.get("value")).sum();

            for (Map<String, Object> item : chartData) {
                Row row = sheet.createRow(rowNum++);
                String name = (String) item.get("name");
                Integer value = (Integer) item.get("value");
                double percentage = totalCount > 0 ? (double) value / totalCount * 100 : 0;

                row.createCell(0).setCellValue(name);
                row.createCell(1).setCellValue(value);
                row.createCell(2).setCellValue(String.format("%.1f%%", percentage));

                for (int i = 0; i < 3; i++) {
                    row.getCell(i).setCellStyle(dataStyle);
                }
            }

            // 自动调整列宽
            for (int i = 0; i < 3; i++) {
                sheet.autoSizeColumn(i);
            }
        }
    }

    /**
     * 创建温度区间分布表
     */
    private void createTempStatsSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle,
                                     HouseStatsAnalysisQuery query) {
        Sheet sheet = workbook.createSheet("温度区间分布");

        Map<String, Object> tempData = getTempChartData(query);

        int rowNum = 0;

        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("室内温度区间分布统计");
        titleCell.setCellStyle(headerStyle);

        // 空行
        rowNum++;

        // 表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {"温度区间", "住户数量", "占比"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 数据行
        @SuppressWarnings("unchecked")
        List<String> categories = (List<String>) tempData.get("categories");
        @SuppressWarnings("unchecked")
        List<Integer> values = (List<Integer>) tempData.get("values");

        int totalCount = values.stream().mapToInt(Integer::intValue).sum();

        for (int i = 0; i < categories.size(); i++) {
            Row row = sheet.createRow(rowNum++);
            String category = categories.get(i);
            Integer value = values.get(i);
            double percentage = totalCount > 0 ? (double) value / totalCount * 100 : 0;

            row.createCell(0).setCellValue(category);
            row.createCell(1).setCellValue(value);
            row.createCell(2).setCellValue(String.format("%.1f%%", percentage));

            for (int j = 0; j < 3; j++) {
                row.getCell(j).setCellStyle(dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建详细统计表
     */
    private void createDetailStatsSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle,
                                       HouseStatsAnalysisQuery query) {
        Sheet sheet = workbook.createSheet("详细统计数据");

        // 获取详细数据
        HouseStatsAnalysisQuery detailQuery = new HouseStatsAnalysisQuery();
        detailQuery.setHeatUnitId(query.getHeatUnitId());
        detailQuery.setStartDate(query.getStartDate());
        detailQuery.setEndDate(query.getEndDate());
        detailQuery.setPage(1);
        detailQuery.setLimit(1000); // 导出时获取更多数据

        Map<String, Object> listData = getStatsList(detailQuery);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> list = (List<Map<String, Object>>) listData.get("list");

        int rowNum = 0;

        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("详细统计数据");
        titleCell.setCellStyle(headerStyle);

        // 空行
        rowNum++;

        // 表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {"统计时间", "小区名称", "阀门状态统计", "温度区间统计"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 数据行
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map<String, Object> item : list) {
            Row row = sheet.createRow(rowNum++);

            Date statTime = (Date) item.get("statTime");
            row.createCell(0).setCellValue(statTime != null ? sdf.format(statTime) : "");
            row.createCell(1).setCellValue((String) item.get("heatUnitName"));
            row.createCell(2).setCellValue((String) item.get("valveStats"));
            row.createCell(3).setCellValue((String) item.get("tempStats"));

            for (int i = 0; i < 4; i++) {
                row.getCell(i).setCellStyle(dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < 4; i++) {
            sheet.autoSizeColumn(i);
        }
    }
}
